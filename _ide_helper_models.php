<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $user_id
 * @property string $model
 * @property \App\Enums\ActivityActionType $action
 * @property array|null $attributes
 * @property array|null $data
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|Activity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Activity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Activity query()
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Activity whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperActivity {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $advice_language_id
 * @property string $title
 * @property string $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\AdviceLanguage $adviceLanguage
 * @method static \Illuminate\Database\Eloquent\Builder|Advice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Advice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Advice query()
 * @method static \Illuminate\Database\Eloquent\Builder|Advice whereAdviceLanguageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advice whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advice whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advice whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperAdvice {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Advice> $advices
 * @property-read int|null $advices_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdviceLanguage whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperAdviceLanguage {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $health_center_id
 * @property \App\Enums\UserRole|null $user_role
 * @property string $title
 * @property string $body
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthCenter|null $healthCenter
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign query()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereUserRole($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCampaign {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property \App\Enums\CompanionType $role
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanionRole whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCompanionRole {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property string $token
 * @property int $expired
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken query()
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken whereExpired($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FcmToken whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFcmToken {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $health_center_id
 * @property string $user_id
 * @property \App\Enums\UserResponsibility $responsibility
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthCenter $healthCenter
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser whereResponsibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenterUser whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHealthCenterUser {}
}

namespace App\Models\Health{
/**
 * 
 *
 * @property string $id
 * @property string $health_zone_id
 * @property string $name
 * @property string $address
 * @property string $phone
 * @property string|null $services_offered
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthZone $healthZone
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Instance> $instances
 * @property-read int|null $instances_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Relapse> $relapses
 * @property-read int|null $relapses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Survivor> $survivors
 * @property-read int|null $survivors_count
 * @property-read \App\Models\HealthCenterUser $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter query()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereHealthZoneId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereServicesOffered($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthCenter withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHealthCenter {}
}

namespace App\Models\Health{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property int|null $population_served
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $province
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Health\HealthCenter> $healthCenters
 * @property-read int|null $health_centers_count
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone query()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone wherePopulationServed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|HealthZone withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHealthZone {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $question
 * @property \App\Enums\QuestionnaireResponseType $type
 * @property string|null $hint
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\QuestionnaireChoice> $choices
 * @property-read int|null $choices_count
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire query()
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire whereHint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire whereQuestion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Questionnaire whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperQuestionnaire {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $questionnaire_id
 * @property string $choice
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice query()
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice whereChoice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice whereQuestionnaireId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QuestionnaireChoice whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperQuestionnaireChoice {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $phone
 * @property string $token
 * @property string $password
 * @property \Illuminate\Support\Carbon $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResetPasswordRequest whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperResetPasswordRequest {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string|null $email
 * @property string $phone
 * @property \App\Enums\UserRole $role
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property mixed $password
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\CompanionRole|null $companionRole
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Companion> $companions
 * @property-read int|null $companions_count
 * @property-read \App\Models\FcmToken|null $fcmToken
 * @property-read \App\Models\HealthCenterUser $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Health\HealthCenter> $healthCenters
 * @property-read int|null $health_centers_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|User withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperUser {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property \App\Enums\CompanionType $type
 * @property string $instance_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Followup> $followups
 * @property-read int|null $followups_count
 * @property-read \App\Models\Victim\Instance $instance
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Companion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Companion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Companion onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Companion query()
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Companion withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Companion withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCompanion {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $instance_id
 * @property string $questionnaire_id
 * @property string $response
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @property-read \App\Models\Questionnaire $questionnaire
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic query()
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic whereQuestionnaireId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Diagnostic whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperDiagnostic {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string $description
 * @property string $instance_id
 * @property string|null $companion_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Companion|null $companion
 * @method static \Illuminate\Database\Eloquent\Builder|Followup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Followup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Followup query()
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereCompanionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Followup whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFollowup {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $health_center_id
 * @property string $survivor_id
 * @property \App\Enums\InstanceType $type
 * @property \App\Enums\InstanceStatus $status
 * @property string $code
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $closed
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Companion> $companions
 * @property-read int|null $companions_count
 * @property-read \App\Models\Victim\InstanceCreator|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Diagnostic> $diagnostics
 * @property-read int|null $diagnostics_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Followup> $followups
 * @property-read int|null $followups_count
 * @property-read \App\Models\Health\HealthCenter $healthCenter
 * @property-read mixed $opened
 * @property-read \App\Models\Victim\Relapse|null $relapse
 * @property-read \App\Models\Victim\Survivor $survivor
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Treatment> $treatments
 * @property-read int|null $treatments_count
 * @method static \Illuminate\Database\Eloquent\Builder|Instance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Instance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Instance onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Instance query()
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereSurvivorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Instance withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Instance withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInstance {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property string $instance_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator query()
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstanceCreator whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInstanceCreator {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $instance_id
 * @property string $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse query()
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Relapse whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperRelapse {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $code
 * @property string|null $description
 * @property string $health_center_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthCenter $healthCenter
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor query()
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Survivor withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperSurvivor {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property \App\Enums\TreatmentType $type
 * @property string $observation
 * @property string|null $attachment
 * @property string $instance_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereAttachment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereObservation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Treatment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperTreatment {}
}

