{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "appstract/laravel-opcache": "^4.0", "filament/filament": "^3.2", "flowframe/laravel-trend": "^0.2.0", "guzzlehttp/guzzle": "^7.8", "laravel-notification-channels/fcm": "^4.3", "laravel/folio": "^1.1", "laravel/framework": "^11.0", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "predis/predis": "^2.0", "propaganistas/laravel-phone": "^5.2", "spatie/laravel-backup": "^8.6", "spatie/laravel-pdf": "^1.5", "twilio/sdk": "^8.0", "valentin-morice/filament-json-column": "^1.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.34", "pestphp/pest-plugin-laravel": "^2.3", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}