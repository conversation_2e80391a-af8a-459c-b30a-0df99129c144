<?php

use App\Http\Controllers\API;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;


Route::post('/auth/token', API\ApiTokenController::class);

Route::get('/advices', [API\AdviceController::class, 'index']);

Route::get('/advices/{advice}', [API\AdviceController::class, 'show']);

Route::prefix('password')->group(function () {
    Route::post('/forgot', API\Password\ForgotPasswordController::class);

    Route::post('/reset', API\Password\ResetPasswordController::class);
});

Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('user')->group(function () {
        Route::get('/', [API\AuthUserController::class, 'me']);

        Route::get('/fcm-token', [API\FCMTokenController::class, 'index']);

        Route::post('/fcm-token', [API\FCMTokenController::class, 'store']);

        Route::delete('/fcm-token', [API\FCMTokenController::class, 'delete']);

        Route::get('/notifications', [API\NotificationController::class, 'index']);

        Route::get('/notifications/unread', [API\NotificationController::class, 'unread']);

        Route::post('/notifications/mark-all-as-read', [API\NotificationController::class, 'markAllAsRead']);

        Route::put('/notifications/{notification}/mark-as-read', [API\NotificationController::class, 'markAsRead']);

        Route::delete('/notifications/{notification}', [API\NotificationController::class, 'delete']);

        Route::delete('/notifications', [API\NotificationController::class, 'deleteAll']);
    });


    Route::middleware('auth.aps')->group(function () {
        Route::get('/questionnaires', [API\QuestionnaireController::class, 'index']);

        Route::get('/questionnaires/{questionnaire}/choices', [API\QuestionnaireController::class, 'choices']);

        Route::get('/health-centers', [API\HealthCenterController::class, 'index']);

        Route::get('/instances', [API\InstanceController::class, 'index']);

        Route::get('/instances/{instance}', [API\InstanceController::class, 'show']);

        Route::get('/instances/relapsed', [API\InstanceRelapsedController::class, 'index']);


        // Attach APS to health center
        Route::get('/health-centers/{health_center}/aps/attach', [API\HealthCenter\APSAttachController::class, 'search']);

        Route::post('/health-centers/{health_center}/aps/attach', [API\HealthCenter\APSAttachController::class, 'attach']);

        // Attach Companion to health center
        Route::get('/health-centers/{health_center}/companions/attach', [API\HealthCenter\CompanionAttachController::class, 'search']);

        Route::post('/health-centers/{health_center}/companions/attach', [API\HealthCenter\CompanionAttachController::class, 'attach']);

        // Health Center APIs resources
        Route::apiResource('health-centers.aps', API\HealthCenter\APSController::class)->parameters([
            'aps' => 'user'
        ]);

        Route::apiResource('health-centers.companions', API\HealthCenter\CompanionController::class)->parameters([
            'companions' => 'user'
        ]);

        Route::apiResource('health-centers.instances', API\HealthCenter\InstanceController::class);


        Route::apiResource('health-centers.instances-relapsed', API\HealthCenter\InstanceRelapsedController::class)->only(['index']);

        // instances
        Route::apiResource('instances.diagnostics', API\Instance\DiagnosticController::class);

        Route::apiResource('instances.treatments', API\Instance\TreatmentController::class);

        Route::apiResource('instances.companions', API\Instance\CompanionController::class)->withTrashed();

        Route::apiResource('instances.followups', API\Instance\FollowupController::class)->only(['index', 'show', 'destroy']);

        Route::apiSingleton('instances.relapse', API\Instance\RelapseController::class)->creatable();
    });


    Route::middleware('auth.companion')->group(function () {
        Route::apiResource('companions', API\Companion\CompanionController::class)->only(['index', 'show']);

        Route::apiSingleton('companions.relapse', API\Companion\RelapseController::class)->creatable();

        Route::apiResource('companions.followups', API\Companion\FollowupController::class);
    });
});
