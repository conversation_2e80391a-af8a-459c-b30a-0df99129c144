<?php

namespace App\Events;

use App\Enums\ActivityActionType;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RecordActivityEvent
{
    use Dispatchable;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly string $model,
        public readonly ActivityActionType $action,
        public readonly ?array $data,
        public readonly ?array $attributes,
        public readonly mixed $user = null
    ) {
        //
    }
}
