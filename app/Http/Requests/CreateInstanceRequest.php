<?php

namespace App\Http\Requests;

use App\Enums;
use App\Models\Victim\Instance;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateInstanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'survivor_code' => ['required', 'string', 'max:50'],
            'code' => ['required', 'string', 'unique:instances', 'max:50'],
            'type' => ['required', Rule::enum(Enums\InstanceType::class)],
            'description' => ['nullable', 'string'],
        ];
    }


    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge(['code' => Instance::getUniqueCode()]);
    }
}
