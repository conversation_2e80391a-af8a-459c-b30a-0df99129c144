<?php

namespace App\Http\Requests;

use App\Enums;
use Illuminate\Validation\Rule;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;

class CreateInstanceCompanionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $instance = $this->route('instance');

        return [
            'type' => ['required', Rule::enum(Enums\CompanionType::class)],
            'user_id' => [
                'required',
                Rule::exists('health_center_user', 'user_id')->where(function (Builder $query) use ($instance) {
                    return $query->where('health_center_id', $instance->health_center_id)
                        ->where('responsibility', Enums\UserResponsibility::COMPANION);
                }),
                Rule::unique('companions')->where(fn(Builder $query) => $query->where('instance_id', $instance->id))
            ]
        ];
    }
}
