<?php

namespace App\Http\Resources\Followup;

use App\Http\Resources\Companion\CompanionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FollowupShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'companion' => $this->when(
                !$request->user()->isCompanion() && filled($this->companion),
                fn() => (new CompanionResource($this->companion))->toArray($request)
            ),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
