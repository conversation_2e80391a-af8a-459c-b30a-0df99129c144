<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'responsibility' => $this->when(
                filled($this->pivot),
                fn() => $this->pivot->responsibility,
            ),
            'companion_role' => $this->when(
                filled($this->companionRole),
                fn() => $this->companionRole->role,
            ),
            'role' => $this->role,
            'phone' => $this->phone,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
