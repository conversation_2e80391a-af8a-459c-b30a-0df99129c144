<?php

namespace App\Http\Resources\Diagnostic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Questionnaire\QuestionnaireResource;


class DiagnosticResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'questionnaire' => (new QuestionnaireResource($this->questionnaire))->toArray($request),
            'instance_id' => $this->instance_id,
            'response' => $this->response,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
