<?php

namespace App\Http\Resources\Advice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdviceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'language' => $this->adviceLanguage->name,
            'title' => $this->title,
            'description' => "",
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
