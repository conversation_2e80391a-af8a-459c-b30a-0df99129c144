<?php

namespace App\Http\Resources\HealthCenter;

use App\Http\Resources\HealthZone\HealthZoneResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HealthCenterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'address' => $this->address,
            'phone' => $this->phone,
            'services_offered' => $this->services_offered,
            'aps' => $this->aps()->count(),
            'companions' => $this->companions()->count(),
            'instances' => $this->instances()->count(),
            'relapses' => $this->relapses()->count(),
            'responsibility' => $this->when(
                filled($this->pivot),
                fn() => $this->pivot->responsibility,
            ),
            'health_zone' => (new HealthZoneResource($this->healthZone))->toArray($request),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
