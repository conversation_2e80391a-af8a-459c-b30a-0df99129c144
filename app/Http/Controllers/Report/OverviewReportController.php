<?php

namespace App\Http\Controllers\Report;

use function Spatie\LaravelPdf\Support\pdf;
use App\Http\Controllers\Controller;
use App\Models;
use Illuminate\Http\Request;

class OverviewReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $healthCenters = Models\Health\HealthCenter::all();

        $instanceCount = Models\Victim\Instance::count();
        $relapseCount = Models\Victim\Relapse::count();
        $healthZoneCount = Models\Health\HealthZone::count();
        $healthCenterCount = Models\Health\HealthCenter::count();

        $appname = config('app.name');
        $today = now()->format("d-m-Y");

        return pdf()
            ->margins(top: 32, right: 32, bottom: 32, left: 32, unit: "px")
            ->view('reports.overview', [
                'instanceCount' => $instanceCount,
                'relapseCount' => $relapseCount,
                'healthZoneCount' => $healthZoneCount,
                'healthCenterCount' => $healthCenterCount,
                'healthCenters' => $healthCenters
            ])
            ->name("$appname-report-overview-$today.pdf");
    }
}
