<?php

namespace App\Http\Controllers\API;

use App\Http\Resources\Advice as AdviceResource;
use App\Models\Advice;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;


class AdviceController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function index(Request $request)
    {
        $advices = Advice::paginate(80);

        return new AdviceResource\AdviceCollection($advices);
    }

    public function show(Request $request, Advice $advice)
    {
        return new AdviceResource\AdviceShowResource($advice);
    }
}
