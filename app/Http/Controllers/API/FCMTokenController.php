<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FCMTokenController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        return [
            'token' => $user->fcmToken?->token,
            'expired' => $user->fcmToken?->expired_at,
        ];
    }


    /**
     * Handle the incoming request.
     */
    public function store(Request $request)
    {
        $data = $request->validate(['fcm_token' => 'required|string']);

        $user = $request->user();

        $user->fcmToken()->updateOrCreate([], ['token' => $data['fcm_token']]);

        return ['message' => 'FCM token updated successfully'];
    }


    /**
     * Handle the incoming request.
     */
    public function delete(Request $request)
    {
        $user = $request->user();

        if ($user->fcmToken) {
            $user->fcmToken->delete();
        }

        return ['message' => 'FCM token deleted successfully'];
    }
}
