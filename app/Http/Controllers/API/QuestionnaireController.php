<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Models\Questionnaire;
use App\Http\Controllers\Controller;
use App\Http\Resources\Questionnaire\QuestionnaireCollection;
use App\Http\Resources\QuestionnaireChoice\QuestionnaireChoiceCollection;

class QuestionnaireController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return new QuestionnaireCollection(Questionnaire::all());
    }

    /**
     * Display a listing of the resource choices.
     */
    public function choices(Questionnaire $questionnaire)
    {
        return new QuestionnaireChoiceCollection($questionnaire->choices);
    }
}
