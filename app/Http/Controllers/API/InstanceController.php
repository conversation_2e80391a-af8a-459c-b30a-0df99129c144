<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\Instance\InstanceCollection;
use App\Http\Resources\Instance\InstanceShowResource;
use App\Models\Victim\Instance;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;


class InstanceController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-instance-healthCenter', only: ['show']),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $query = $this->getInstancesQuery($user)->latest();

        if ($search = $request->query('search')) {
            $query = $query->whereLike(['survivor.code', 'code'], $search);
        }

        return new InstanceCollection($query->paginate(30));
    }

    /**
     * Display the specified resource.
     */
    public function show(Instance $instance)
    {
        return new InstanceShowResource($instance);
    }

    private function getInstancesQuery($user)
    {
        if ($user->isRoot()) {
            return Instance::with(['survivor', 'healthCenter']);
        }

        $healthCentersIds = $user->healthCenters()->pluck('health_centers.id')->toArray();

        return Instance::with(['survivor', 'healthCenter'])->whereIn('health_center_id', $healthCentersIds);
    }
}
