<?php

namespace App\Http\Controllers\API\Companion;

use App\Http\Controllers\Controller;
use App\Http\Resources\Companion\CompanionCollection;
use App\Http\Resources\Companion\CompanionResource;
use App\Models\Victim\Companion;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;


class CompanionController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-companion-healthCenter', except: ['index']),
        ];
    }


    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $companions = $user->companions()
            ->with([
                'instance',
                'instance.relapse',
                'instance.healthCenter',
                'instance.survivor',
            ])->latest()->paginate(30);

        return new CompanionCollection($companions);
    }

    /**
     * Display the specified resource.
     */
    public function show(Companion $companion)
    {
        return new CompanionResource($companion);
    }
}
