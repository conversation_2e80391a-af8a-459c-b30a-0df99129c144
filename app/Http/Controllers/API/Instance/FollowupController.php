<?php

namespace App\Http\Controllers\API\Instance;

use App\Http\Resources\Followup\FollowupCollection;
use App\Http\Resources\Followup\FollowupShowResource;
use App\Models\Victim\Instance;
use Illuminate\Http\Request;
use App\Models\Victim\Followup;
use App\Http\Controllers\Controller;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class FollowupController extends Controller implements HasMiddleware
{

    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-instance-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Instance $instance)
    {
        $followups = $instance->followups()->latest()->paginate(10);

        return new FollowupCollection($followups);
    }

    /**
     * Display the specified resource.
     */
    public function show(Instance $instance, Followup $followup)
    {
        return new FollowupShowResource($followup);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Instance $instance, Followup $followup)
    {
        $followup->deleteOrFail();

        return [
            'message' => __('Suivi supprimé avec succès')
        ];
    }
}
