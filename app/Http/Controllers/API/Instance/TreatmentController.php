<?php

namespace App\Http\Controllers\API\Instance;

use App\Http\Controllers\Controller;
use App\Http\Resources\Treatment\TreatmentCollection;
use App\Http\Resources\Treatment\TreatmentShowResource;
use App\Models\Victim\Instance;
use App\Models\Victim\Treatment;
use App\Http\Requests;
use App\Validator\Attachment;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Storage;

class TreatmentController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-instance-healthCenter'),
        ];
    }


    /**
     * Display a listing of the resource.
     */
    public function index(Instance $instance)
    {
        return new TreatmentCollection($instance->treatments);
    }

    /**
     * Display the specified resource.
     */
    public function show(Instance $instance, Treatment $treatment)
    {
        $treatment = $instance->treatments()->where('treatments.id', $treatment->id)->firstOrFail();

        return new TreatmentShowResource($treatment);
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Requests\CreateTreatmentRequest $request, Instance $instance)
    {
        $data = $request->validated();

        if ($request->hasFile('attachment')) {
            $data['attachment'] = $request->file('attachment')->store(Attachment::TREATMENT_UPLOAD_DIRECTORY, 'public');
        }

        $treatment = $instance->treatments()->create($data);

        return new TreatmentShowResource($treatment);
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Requests\UpdateTreatmentRequest $request, Instance $instance, Treatment $treatment)
    {
        $data = $request->validated();

        if ($request->hasFile('attachment')) {
            if (filled($treatment->attachment)) {
                Storage::disk('public')->delete($treatment->attachment);
            }

            $data['attachment'] = $request->file('attachment')->store(Attachment::TREATMENT_UPLOAD_DIRECTORY, 'public');
        }

        $treatment->fill($data);
        $treatment->save();

        $treatment->refresh();

        return new TreatmentShowResource($treatment);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Instance $instance, Treatment $treatment)
    {
        $treatment->deleteOrFail();

        return [
            'message' => __("La prise en charge a été supprimée avec succès.")
        ];
    }
}
