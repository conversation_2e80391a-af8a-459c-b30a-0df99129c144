<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\Instance\InstanceCollection;
use App\Models\Victim\Instance;
use Illuminate\Http\Request;

class InstanceRelapsedController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $query = $this->getInstancesQuery($user)->latest();

        if ($search = $request->query('search')) {
            $query = $query->whereLike(['survivor.code', 'code'], $search);
        }

        return new InstanceCollection($query->paginate(30));
    }


    private function getInstancesQuery($user)
    {
        if ($user->isRoot()) {
            return Instance::with(['survivor', 'healthCenter'])
                ->has('relapse');
        }

        $healthCentersIds = $user->healthCenters()->pluck('health_centers.id')->toArray();

        return Instance::with(['survivor', 'healthCenter'])
            ->has('relapse')
            ->whereIn('health_center_id', $healthCentersIds);
    }
}
