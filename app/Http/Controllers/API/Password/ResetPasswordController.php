<?php

namespace App\Http\Controllers\API\Password;

use App\Http\Controllers\Controller;
use App\Models;
use App\Http\Requests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ResetPasswordController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Requests\ResetPasswordRequest $request)
    {
        $data = $request->validated();

        $resetRequest = Models\ResetPasswordRequest::where($data)->where('expires_at', '>', now())->first();

        abort_if(!$resetRequest, 404, __("Le code de réinitialisation ou mot de passe n'est pas valide."));

        $user = Models\User::where('phone', $data['phone'])->first();

        abort_if(!$user, 404, __("Le numéro de téléphone n'est pas valide."));

        $user->fill(['password' => Hash::make($data['password'])]);
        $user->saveQuietly();

        $resetRequest->delete();

        event(new \App\Events\PasswordReset($user));

        return [
            'message' => __("Votre mot de passe a été réinitialisé avec succès.")
        ];
    }
}
