<?php

namespace App\Filament\Widgets;

use App\Models\Victim\Instance;
use App\Models\Victim\Relapse;
use Illuminate\Support\Carbon;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use App\Models\Health\HealthCenter;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class StatsOverviewWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 0;

    public static function canView(): bool
    {
        return auth()->user()->isRoot();
    }

    protected function getStats(): array
    {
        $startDate = !is_null($this->filters['startDate'] ?? null) ?
            Carbon::parse($this->filters['startDate']) :
            now()->subMonths(2);

        $endDate = !is_null($this->filters['endDate'] ?? null) ?
            Carbon::parse($this->filters['endDate']) :
            now();

        $healthCenterId = $this->filters['healthCenter'] ?? null;

        $instanceQuery = match ($healthCenterId) {
            null => Instance::query(),
            default => HealthCenter::where('id', $healthCenterId)
                ->first()
                ->instances()
                ->getQuery(),
        };

        $relapseQuery = match ($healthCenterId) {
            null => Relapse::query(),
            default => HealthCenter::where('id', $healthCenterId)
                ->first()
                ->relapses()
                ->getQuery(),
        };

        $relapseData = Trend::query($relapseQuery)
            ->dateColumn('relapses.created_at')
            ->between(start: $startDate, end: $endDate)
            ->perMonth()
            ->count();


        $instanceData = Trend::query($instanceQuery)
            ->dateColumn('instances.created_at')
            ->between(start: $startDate, end: $endDate)
            ->perMonth()
            ->count();



        $formatNumber = function (int $number): string {
            if ($number < 1000) {
                return (string) Number::format($number, 0);
            }

            if ($number < 1000000) {
                return Number::format($number / 1000, 2) . 'k';
            }

            return Number::format($number / 1000000, 2) . 'm';
        };

        return [
            Stat::make(__('Rechutes'), $formatNumber($relapseData->sum('aggregate')))
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->chart($relapseData->map(fn(TrendValue $value) => $value->aggregate)->toArray())
                ->color('danger'),

            Stat::make(__("Nouveaux cas"), $formatNumber($instanceData->sum('aggregate')))
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->chart($instanceData->map(fn(TrendValue $value) => $value->aggregate)->toArray())
                ->color('info'),

            Stat::make(__("Centres de Santé"), $formatNumber(HealthCenter::count()))
                ->icon('heroicon-o-circle-stack')
                ->color('info'),
        ];
    }
}
