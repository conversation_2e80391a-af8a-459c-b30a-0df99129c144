<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AdviceResource\Pages;
use App\Filament\Resources\AdviceResource\RelationManagers;
use App\Models\Advice;
use App\Validator\Attachment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AdviceResource extends Resource
{
    protected static ?string $model = Advice::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationGroup = 'Global';

    protected static ?string $navigationLabel = 'Conseils';


    protected static ?string $modelLabel = 'Conseil';


    protected static ?string $pluralModelLabel = 'Conseils';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('advice_language_id')
                    ->native(false)
                    ->label(__("Langue"))
                    ->relationship('adviceLanguage', 'name')
                    ->preload()
                    ->searchable()
                    ->required()
                    ->lazy()
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name')
                            ->label(__("Nom de la langue"))
                            ->required()
                            ->disabledOn('edit')
                            ->unique(ignoreRecord: true)
                            ->maxLength(50),
                    ])
                    ->createOptionAction(function (Action $action) {
                        return $action
                            ->modalHeading(__("Créer Langue"))
                            ->modalSubmitActionLabel(__("Créer Langue"))
                            ->modalWidth('md');
                    }),


                Forms\Components\TextInput::make('title')
                    ->label(__('Titre'))
                    ->required(),


                Forms\Components\RichEditor::make('description')
                    ->label(__('Contenu'))
                    ->fileAttachmentsDirectory(Attachment::ADVICE_UPLOAD_DIRECTORY)
                    ->required()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('adviceLanguage.name')
                    ->label(label: __("Langue"))
                    ->sortable(),


                Tables\Columns\TextColumn::make('title')
                    ->label(label: __('Titre'))
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->sortable()
                    ->label(label: __('Ajouté le'))
                    ->toggleable(),


                Tables\Columns\TextColumn::make('updated_at')
                    ->since()
                    ->label(label: __('Modifié le'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([])
            ->paginated([10, 25, 50])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdvice::route('/'),
            'create' => Pages\CreateAdvice::route('/create'),
            'edit' => Pages\EditAdvice::route('/{record}/edit'),
        ];
    }
}
