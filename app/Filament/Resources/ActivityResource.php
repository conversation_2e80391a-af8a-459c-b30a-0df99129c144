<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ActivityResource\Pages;
use App\Filament\Resources\ActivityResource\RelationManagers;
use App\Models\Activity;
use Filament\Forms;
use Filament\Forms\Form;
use ValentinMorice\FilamentJsonColumn\JsonColumn;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ActivityResource extends Resource
{
    protected static ?string $model = Activity::class;

    protected static ?int $navigationSort = 8;

    protected static ?string $navigationGroup = 'Système';

    protected static ?string $navigationLabel = 'Activités';

    protected static ?string $modelLabel = 'Activité';

    protected static ?string $pluralModelLabel = 'Activités';

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path-rounded-square';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label(__("Utilisateur"))
                    ->relationship('user', 'name'),


                Forms\Components\TextInput::make('model')
                    ->label(__("Modèle"))
                    ->required(),


                Forms\Components\Textarea::make('action')
                    ->label(__("Action"))
                    ->required()
                    ->columnSpanFull(),


                JsonColumn::make('attributes')
                    ->label(__("Attributs modifiés"))
                    ->viewerOnly()
                    ->columnSpanFull(),


                JsonColumn::make('data')
                    ->label(__("Données"))
                    ->viewerOnly()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__("Utilisateur"))
                    ->searchable(),


                Tables\Columns\TextColumn::make('model')
                    ->label(__("Modèle"))
                    ->searchable(),


                Tables\Columns\BadgeColumn::make('action')
                    ->label(__("Action"))
                    ->sortable(),


                Tables\Columns\TextColumn::make('created_at')
                    ->label(__("Créé le"))
                    ->since()
                    ->sortable(),
            ])
            ->filters([])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivities::route('/'),
            'view' => Pages\ViewActivity::route('/{record}'),
        ];
    }
}
