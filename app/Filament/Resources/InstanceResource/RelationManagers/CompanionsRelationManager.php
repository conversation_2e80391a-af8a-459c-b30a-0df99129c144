<?php

namespace App\Filament\Resources\InstanceResource\RelationManagers;

use App\Enums;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Get;


class CompanionsRelationManager extends RelationManager
{
    protected static string $relationship = 'companions';

    protected static ?string $modelLabel = "Accompagnant";

    protected static ?string $title = 'Accompagnants';


    public function form(Form $form): Form
    {
        [$AllCompanions, $companions] = once(function () {
            /**
             * @var \App\Models\Victim\Instance
             */
            $instance = $this->getOwnerRecord();
            $companions = $instance->companions->pluck('user_id')->all();

            $allCompanion = $instance->healthCenter->users()
                ->where("responsibility", Enums\UserResponsibility::COMPANION)
                ->pluck("users.name", 'users.id');

            return [
                $allCompanion->all(),
                $allCompanion->filter(fn($v, $k) => !in_array($k, $companions))->all()
            ];
        });

        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->searchable()
                    ->options($AllCompanions)
                    ->visibleOn('edit')
                    ->disabled()
                    ->label(__('Accompagnant'))
                    ->placeholder(__('Sélectionner un accompagnant'))
                    ->native(false)
                    ->required(),




                Forms\Components\Select::make('user_id')
                    ->searchable()
                    ->options($companions)
                    ->visibleOn('create')
                    ->label(__('Accompagnant'))
                    ->placeholder(__('Sélectionner un accompagnant'))
                    ->native(false)
                    ->live()
                    ->required(),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\ToggleButtons::make('type')
                            ->inline()
                            ->required()
                            ->options(function (Get $get) {
                                if (null === $get("user_id")) {
                                    return [];
                                }
                                $user = User::find($get("user_id"));

                                if ($user->companionRole) {
                                    $role = $user->companionRole->role;
                                    return [$role->value => $role->getLabel()];
                                }

                                return Enums\CompanionType::class;
                            }),
                    ])

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                return $query->with('user')
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);
            })
            ->recordTitleAttribute('user.name')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__("Nom D'accompagnant")),


                Tables\Columns\TextColumn::make('user.phone')
                    ->label(__("Numéro de téléphone")),


                Tables\Columns\TextColumn::make('type')->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__("Ajouté le")),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ]);
    }


    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
