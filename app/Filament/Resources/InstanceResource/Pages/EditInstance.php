<?php

namespace App\Filament\Resources\InstanceResource\Pages;

use App\Filament\Resources\InstanceResource;
use App\Models\Victim\Instance;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditInstance extends EditRecord
{
    protected static string $resource = InstanceResource::class;

    protected function getHeaderActions(): array
    {
        $instance = $this->getRecord();

        return [
            Actions\Action::make('report')
                ->color("info")
                ->label(__("Générer le rapport"))
                ->url(route('report.instance', ['id' => $instance->id])),


            Actions\Action::make('open_close')
                ->color($instance->opened ? "warning" : "info")
                ->action(fn(Instance $record) => $record->toggleStatus())
                ->label($instance->opened ? __("Décharger le cas") : __("Relancer le cas"))
                ->requiresConfirmation(),


            Actions\DeleteAction::make(),


            Actions\ForceDeleteAction::make(),


            Actions\RestoreAction::make(),
        ];
    }
}
