<?php

namespace App\Filament\Resources\UserResource\RelationManagers;


use App\Enums;
use App\Filament\Resources\HealthCenterResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;


class HealthCentersRelationManager extends RelationManager
{
    protected static string $relationship = 'healthCenters';

    protected static ?string $modelLabel = "Centre de santé";

    protected static ?string $title = 'Centres de santé';

    public function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__("Nom du centre de santé")),

                Tables\Columns\TextColumn::make('responsibility')
                    ->label(__('Responsabilité'))
                    ->badge(),
            ])
            ->filters([])
            ->headerActions([])
            ->actions([
                Tables\Actions\LinkAction::make(__("Voir Plus"))
                    ->color("gray")
                    ->url(function (Tables\Actions\LinkAction $action) {
                        $record = $action->getRecord();
                        return HealthCenterResource::getUrl("view", ['record' => $record->id]);
                    }),
            ])
            ->bulkActions([]);
    }


    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return !$ownerRecord->isRoot();
    }
}
