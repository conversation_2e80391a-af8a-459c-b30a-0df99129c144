<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HealthCenterResource\Pages;
use App\Filament\Resources\HealthCenterResource\RelationManagers;
use App\Models\Health\HealthCenter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Actions\Action;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HealthCenterResource extends Resource
{
    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Centres de santé';

    protected static ?string $modelLabel = 'Centre de santé';

    protected static ?string $pluralModelLabel = 'Centres de santé';

    protected static ?string $model = HealthCenter::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->disabled(!self::hasAccess())
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('health_zone_id')
                            ->native(false)
                            ->label(__("Zone de santé"))
                            ->preload()
                            ->lazy()
                            ->searchable()
                            ->relationship('healthZone', 'name')
                            ->required()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->disabledOn('edit')
                                    ->label(__("Nom"))
                                    ->unique(ignoreRecord: true)
                                    ->required()
                                    ->maxLength(255),


                                Forms\Components\TextInput::make('population_served')
                                    ->label(__("Population"))
                                    ->disabledOn('edit')
                                    ->required(false)
                                    ->numeric(),
                            ])
                            ->createOptionAction(function (Action $action) {
                                return $action
                                    ->modalHeading(__("Créer Zone de santé"))
                                    ->modalSubmitActionLabel(__("Créer Zone de santé"))
                                    ->modalWidth('lg');
                            }),

                        Forms\Components\TextInput::make('name')
                            ->label(__("Nom"))
                            ->unique(ignoreRecord: true)
                            ->required(),

                        Forms\Components\TextInput::make('address')
                            ->label(__("L'adresse"))
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label(__('Numéro de téléphone'))
                            ->tel()
                            ->rules(['phone'])
                            ->required(),

                        Forms\Components\Textarea::make('services_offered')
                            ->label(__('Services proposés'))
                            ->required()
                            ->columnSpanFull(),

                    ])
                    ->columnSpan(['lg' => fn(?HealthCenter $record) => $record === null ? 3 : 2]),



                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->hiddenOn('create')
                            ->label(__("Créé le"))
                            ->content(fn(HealthCenter $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->hiddenOn('create')
                            ->label(__("Mis à jour le"))
                            ->content(fn(HealthCenter $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn(?HealthCenter $record) => $record === null),
            ])
            ->columns(['sm' => 3, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__("Centre de santé"))
                    ->searchable(),


                Tables\Columns\TextColumn::make('healthZone.name')
                    ->label(__("Zone de santé"))
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('address')
                    ->label(__("L'adresse"))
                    ->searchable(),

                Tables\Columns\TextColumn::make('phone')
                    ->label(__('Numéro de téléphone'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__("Supprimé le"))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__("Créé le"))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),


                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__("Mis à jour le"))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ])->hidden(!self::hasAccess()),
            ]);
    }

    public static function hasAccess()
    {
        return auth()->user()->isRoot();
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\APSRelationManager::class,
            RelationManagers\CompanionsRelationManager::class,
            RelationManagers\InstancesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHealthCenters::route('/'),
            'create' => Pages\CreateHealthCenter::route('/create'),
            'view' => Pages\ViewHealthCenter::route('/{record}'),
            'edit' => Pages\EditHealthCenter::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        /**
         * @var \App\Models\User $user
         */
        $user = auth()->user();

        if ($user->isAPS()) {
            $ids = $user->healthCenters()
                ->pluck('health_centers.id')
                ->toArray();

            return parent::getEloquentQuery()
                ->with(['healthZone'])
                ->whereIn('id', $ids);
        }

        return parent::getEloquentQuery()
            ->with(['healthZone'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
