<?php

namespace App\Filament\Resources\HealthCenterResource\RelationManagers;

use App\Enums;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\AttachAction;
use Str;

class CompanionsRelationManager extends RelationManager
{
    protected static string $relationship = 'users';

    protected static ?string $modelLabel = "Accompagnant";

    protected static ?string $title = 'Accompagnants';


    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema(APSRelationManager::getDefaultFormSchema(Enums\UserRole::COMPANION));
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->modifyQueryUsing(fn(Builder $query) => $query->where('role', Enums\UserRole::COMPANION))
            ->columns(APSRelationManager::getDefaultTableSchema(Enums\UserRole::COMPANION))
            ->filters([])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->recordSelectOptionsQuery(
                        fn(Builder $query) => $query->where("role", Enums\UserRole::COMPANION)
                    )
                    ->form(fn(AttachAction $action): array => [
                        $action->getRecordSelect(),
                        Forms\Components\ToggleButtons::make('responsibility')
                            ->inline()
                            ->default(Enums\UserResponsibility::COMPANION)
                            ->label(__('Responsabilité'))
                            ->options(Enums\UserResponsibility::transCompanion())
                            ->required(),
                    ])
                    ->recordSelectSearchColumns(['name', 'phone', 'email']),
                Tables\Actions\CreateAction::make(),
            ])
            ->defaultSort('users.id', 'desc')
            ->actions([
                Tables\Actions\DetachAction::make(),
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
