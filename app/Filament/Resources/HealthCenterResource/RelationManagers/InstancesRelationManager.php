<?php

namespace App\Filament\Resources\HealthCenterResource\RelationManagers;

use App\Enums;
use App\Filament\Resources\InstanceResource;
use App\Models\Victim\Instance;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstancesRelationManager extends RelationManager
{
    protected static string $relationship = 'instances';

    protected static ?string $modelLabel = "Cas";

    protected static ?string $title = 'Cas';


    public function form(Form $form): Form
    {
        $owner = $this->getOwnerRecord();


        return $form
            ->columns(1)
            ->schema([


                Forms\Components\Select::make('survivor_id')
                    ->native(false)
                    ->label(__("Survivant"))
                    ->disabledOn('edit')
                    ->relationship(
                        'survivor',
                        'code',
                        fn(Builder $query) => $query->where('health_center_id', $owner->id)
                    )
                    ->preload()
                    ->searchable()
                    ->required()
                    ->lazy()
                    ->createOptionForm([
                        Forms\Components\Hidden::make('health_center_id')
                            ->default($owner->id)
                            ->required(),


                        Forms\Components\TextInput::make('code')
                            ->label(__("Code"))
                            ->required()
                            ->disabledOn('edit')
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),


                        Forms\Components\Textarea::make('description')
                            ->label(__("Description")),
                    ])
                    ->createOptionAction(function (Action $action) {
                        return $action
                            ->modalHeading(__("Créer Survivant"))
                            ->modalSubmitActionLabel(__("Créer Survivant"))
                            ->modalWidth('md');
                    }),




                Forms\Components\ToggleButtons::make('type')
                    ->label(__('Type'))
                    ->default(Enums\InstanceType::NEW )
                    ->options(Enums\InstanceType::class)
                    ->inline()
                    ->required(),



                Forms\Components\TextInput::make('code')
                    ->default(Instance::getUniqueCode())
                    ->disabled()
                    ->dehydrated()
                    ->required()
                    ->maxLength(32)
                    ->unique(ignoreRecord: true),

                Forms\Components\Textarea::make('description')
                    ->rows(5)
                    ->nullable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('code')
            ->modifyQueryUsing(
                function (Builder $query) {
                    return $query->with('survivor')
                        ->withoutGlobalScopes([
                            SoftDeletingScope::class,
                        ]);
                }
            )
            ->columns([
                Tables\Columns\TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('code')
                    ->searchable(),

                Tables\Columns\BadgeColumn::make('type')->sortable(),

                Tables\Columns\IconColumn::make('status'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__("Ajouté le"))
                    ->since()
                    ->sortable()
                    ->toggleable(),


                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__("Supprimé le"))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->defaultSort('instances.id', 'desc')
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(function (Tables\Actions\ViewAction $action) {
                        $record = $action->getRecord();
                        return InstanceResource::getUrl("edit", ['record' => $record->id]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }
}
