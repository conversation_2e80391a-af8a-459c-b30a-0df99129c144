<?php

namespace App\Filament\Resources\HealthCenterResource\RelationManagers;

use App\Enums;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\AttachAction;
use Str;

class APSRelationManager extends RelationManager
{
    protected static string $relationship = 'users';

    protected static ?string $modelLabel = "APS";

    protected static ?string $title = 'APS';


    public function form(Form $form): Form
    {
        return $form
            ->disabled(!$this->hasAccess())
            ->columns(1)
            ->schema(self::getDefaultFormSchema(Enums\UserRole::APS));
    }

    public static function getDefaultFormSchema(Enums\UserRole $userRole)
    {
        return [
            Forms\Components\TextInput::make('name')
                ->label(__("Nom"))
                ->required()
                ->autocomplete(false)
                ->minLength(3)
                ->maxLength(255),




            Forms\Components\TextInput::make('phone')
                ->label(__('Numéro de téléphone'))
                ->required()
                ->tel()
                ->rules(['phone'])
                ->autocomplete(false)
                ->unique(ignoreRecord: true)
                ->maxLength(32),



            Forms\Components\TextInput::make('email')
                ->email()
                ->label(__('Adresse Email'))
                ->nullable()
                ->autocomplete(false)
                ->unique(ignoreRecord: true)
                ->maxLength(255),




            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\ToggleButtons::make('responsibility')
                        ->label(__('Responsabilité'))
                        ->inline()
                        ->default(
                            match ($userRole) {
                                Enums\UserRole::COMPANION => Enums\UserResponsibility::COMPANION->value,
                                default => null,
                            }
                        )
                        ->options(
                            match ($userRole) {
                                Enums\UserRole::APS => Enums\UserResponsibility::transAPS(),
                                Enums\UserRole::COMPANION => Enums\UserResponsibility::transCompanion(),
                                default => Enums\UserResponsibility::trans(),
                            }
                        )
                        ->required(),




                    Forms\Components\Select::make('role')
                        ->default($userRole->value)
                        ->label(__('Rôle'))
                        ->native(false)
                        ->disabledOn('edit')
                        ->options([$userRole->value => $userRole->getLabel()])
                        ->required(),
                ]),





            ...$userRole === Enums\UserRole::COMPANION ?
            [
                Forms\Components\Fieldset::make()
                    ->relationship('companionRole')
                    ->columns(1)
                    ->schema([
                        Forms\Components\ToggleButtons::make('role')
                            ->label(__('Type Accompagnant'))
                            ->inline()
                            ->required()
                            ->options(Enums\CompanionType::class)
                    ]),
            ] :
            [],







            Forms\Components\Textarea::make('description')
                ->label(__("Plus d'information"))
                ->nullable()
                ->autocomplete(false)
                ->maxLength(255),






            Forms\Components\TextInput::make('password')
                ->label(__('Mot de Passe'))
                ->required()
                ->visibleOn("create")
                ->password()
                ->default(Str::random(10))
                ->readOnly(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->modifyQueryUsing(fn(Builder $query) => $query->where('role', Enums\UserRole::APS))
            ->columns(self::getDefaultTableSchema(Enums\UserRole::APS))
            ->filters([])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->hidden(!$this->hasAccess())
                    ->recordSelectOptionsQuery(
                        fn(Builder $query) => $query->where("role", Enums\UserRole::APS)
                    )
                    ->form(fn(AttachAction $action): array => [
                        $action->getRecordSelect(),

                        Forms\Components\ToggleButtons::make('responsibility')
                            ->inline()
                            ->label(__('Responsabilité'))
                            ->options(Enums\UserResponsibility::transAPS())
                            ->required(),
                    ])
                    ->recordSelectSearchColumns(['name', 'phone', 'email']),

                Tables\Actions\CreateAction::make()
                    ->hidden(!$this->hasAccess()),
            ])
            ->defaultSort('health_center_user.id', 'desc')
            ->actions([
                Tables\Actions\DetachAction::make()
                    ->hidden(!$this->hasAccess()),

                Tables\Actions\EditAction::make()
                    ->hidden(!$this->hasAccess()),

                // Tables\Actions\DeleteAction::make()
                //     ->hidden(!$this->hasAccess()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ])->hidden(!$this->hasAccess()),
            ]);
    }


    public function hasAccess()
    {
        return once(function () {
            $auth = auth()->user();

            if ($auth->isRoot()) {
                return true;
            }


            $owner = $this->getOwnerRecord();

            $auth = $owner->users()
                ->where('health_center_user.user_id', auth()->user()->id)
                ->withPivot(['responsibility'])
                ->first();

            return $auth->pivot->responsibility === Enums\UserResponsibility::ADMINISTRATOR->value;
        });
    }


    public static function getDefaultTableSchema(Enums\UserRole $userRole)
    {
        return [
            Tables\Columns\TextColumn::make('name')
                ->label(__("Nom")),


            Tables\Columns\TextColumn::make('phone')
                ->label(__('Numéro de téléphone')),



            Tables\Columns\TextColumn::make('email')
                ->label(__('Adresse Email')),


            Tables\Columns\TextColumn::make('role')
                ->badge(),

            Tables\Columns\TextColumn::make('responsibility')
                ->label(__('Responsabilité'))
                ->badge(),

            Tables\Columns\TextColumn::make('created_at')
                ->label(__("Créé le"))
                ->since()
                ->sortable()
                ->toggleable(),
        ];
    }

}


