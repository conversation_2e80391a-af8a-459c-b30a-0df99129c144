<?php

namespace App\Filament\Resources;


use App\Enums;
use App\Filament\Resources\InstanceResource\Pages;
use App\Filament\Resources\InstanceResource\RelationManagers;
use App\Models\Victim\Instance;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstanceResource extends Resource
{
    protected static ?string $model = Instance::class;

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationIcon = 'heroicon-o-archive-box-arrow-down';

    protected static ?string $recordTitleAttribute = 'code';

    protected static ?string $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Cas';

    protected static ?string $modelLabel = 'Cas';

    protected static ?string $pluralModelLabel = 'Cas';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('healthCenter.id')
                            ->relationship('healthCenter', 'name')
                            ->label(__("Centre de santé"))
                            ->disabled()
                            ->required(),


                        Forms\Components\Select::make('survivor.id')
                            ->label(__('Code Survivant'))
                            ->relationship('survivor', 'code')
                            ->disabled()
                            ->required(),


                        Forms\Components\ToggleButtons::make('type')
                            ->options(Enums\InstanceType::class)
                            ->inline()
                            ->disabled()
                            ->required(),


                        Forms\Components\TextInput::make('code')
                            ->disabled()
                            ->required(),

                        Forms\Components\Select::make('status')
                            ->options(Enums\InstanceStatus::class)
                            ->disabled()
                            ->required(),


                        Forms\Components\Textarea::make('description')
                            ->rows(5)
                            ->nullable(),

                    ])
                    ->columnSpan(['lg' => fn(?Instance $record) => $record === null ? 3 : 2]),



                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->hiddenOn('create')
                            ->label(__("Créé le"))
                            ->content(fn(Instance $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->hiddenOn('create')
                            ->label(__("Mis à jour le"))
                            ->content(fn(Instance $record): ?string => $record->updated_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('deleted_at')
                            ->hiddenOn('create')
                            ->label(__("Supprimé le"))
                            ->content(fn(Instance $record): ?string => $record->deleted_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn(?Instance $record) => $record === null),
            ])
            ->columns(['sm' => 3, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),


                Tables\Columns\TextColumn::make('healthCenter.name')
                    ->label(__("Centre de santé"))
                    ->searchable(),

                Tables\Columns\TextColumn::make('code')
                    ->searchable(),


                Tables\Columns\BadgeColumn::make('type')
                    ->sortable(),

                Tables\Columns\IconColumn::make('status'),

                Tables\Columns\TextColumn::make('description')
                    ->wrap()
                    ->limit(100),


                Tables\Columns\TextColumn::make('created_at')
                    ->label(__("Créé le"))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__("Supprimé le"))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            RelationManagers\DiagnosticsRelationManager::class,
            RelationManagers\TreatmentsRelationManager::class,
            RelationManagers\CompanionsRelationManager::class,
            RelationManagers\RelapseRelationManager::class,
            RelationManagers\FollowupsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInstances::route('/'),
            'edit' => Pages\EditInstance::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        /**
         * @var \App\Models\User $user
         */
        $user = auth()->user();

        if ($user->isAPS()) {
            $ids = $user->healthCenters()->pluck('health_centers.id')->toArray();

            return parent::getEloquentQuery()
                ->with(['healthCenter', 'survivor', 'companions'])
                ->whereIn('health_center_id', $ids);
        }



        return parent::getEloquentQuery()
            ->with(['healthCenter', 'survivor', 'companions'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
