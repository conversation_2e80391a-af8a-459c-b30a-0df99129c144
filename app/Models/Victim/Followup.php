<?php

namespace App\Models\Victim;

use App\Observers\FollowupObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

/**
 * @mixin IdeHelperFollowup
 */
#[ObservedBy([FollowupObserver::class])]
class Followup extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'companion_id',
        'instance_id',
    ];


    public function companion(): BelongsTo
    {
        return $this->belongsTo(Companion::class);
    }
}
