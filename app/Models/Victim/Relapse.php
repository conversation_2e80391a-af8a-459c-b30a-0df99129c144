<?php

namespace App\Models\Victim;

use App\Observers\RelapseObserver;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

/**
 * @mixin IdeHelperRelapse
 */
#[ObservedBy([RelapseObserver::class])]
class Relapse extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'instance_id',
        'description',
    ];


    public function instance(): Relations\BelongsTo
    {
        return $this->belongsTo(Instance::class);
    }
}
