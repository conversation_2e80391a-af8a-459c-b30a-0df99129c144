<?php

namespace App\Models;

use App\Enums;
use App\Models\Health\HealthCenter;
use App\Observers\HealthCenterUserObserver;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Model;


/**
 * @mixin IdeHelperHealthCenterUser
 */
#[ObservedBy([HealthCenterUserObserver::class])]
class HealthCenterUser extends Pivot
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'health_center_user';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'responsibility' => Enums\UserResponsibility::class,
        ];
    }

    public function healthCenter(): BelongsTo
    {
        return $this->belongsTo(HealthCenter::class);
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
