<?php

namespace App\NotificationChannels\Twilio;

use <PERSON>wi<PERSON>\Rest\Client;
use Twi<PERSON>\Rest\Api\V2010\Account\MessageInstance;

use App\NotificationChannels\Twilio\Exceptions\CouldNotSendNotification;
use stdClass;

class TwilioClient
{
    /**
     * @param Client $client
     */
    public function __construct(
        private readonly Client $twilio,
        private readonly string $twilioMessagingServiceSid,
    ) {
    }

    /**
     * @param string $to
     * @param string $message
     */
    public function send(string $to, string $message)
    {
        $response = $this->twilio->messages->create($to, [
            'body' => $message,
            'messagingServiceSid' => $this->twilioMessagingServiceSid,
        ]);

        $this->handleProviderResponses($response);
    }

    /**
     * @param array $responses
     * @throws CouldNotSendNotification
     */
    protected function handleProviderResponses(MessageInstance $response)
    {
        $errorCode = $response->errorCode;

        if (null !== $errorCode) {
            throw CouldNotSendNotification::serviceRespondedWithAnError(
                (string) $response->errorMessage,
                (int) $errorCode
            );
        }
    }
}
