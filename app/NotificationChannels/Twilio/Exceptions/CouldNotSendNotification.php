<?php

namespace App\NotificationChannels\Twilio\Exceptions;

class CouldNotSendNotification extends \Exception
{
    /**
     * @param string $message
     * @param int $code
     *
     * @return static
     */
    public static function serviceRespondedWithAnError($message, $code)
    {
        return new static(
            "<PERSON><PERSON><PERSON> responded with an error '{$message}: {$code}'"
        );
    }
}
