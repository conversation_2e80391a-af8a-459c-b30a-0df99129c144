<?php

namespace App\NotificationChannels\Twilio;

use Twilio\Api\TwilioHttp;
use Illuminate\Support\ServiceProvider;
use Twilio\Rest\Client;
use Illuminate\Notifications\ChannelManager;
use Illuminate\Support\Facades\Notification;

class TwilioServiceProvider extends ServiceProvider
{
    /**
     * Register the application services.
     */
    public function register()
    {
        $this->app->when(TwilioChannel::class)
            ->needs(TwilioClient::class)
            ->give(function () {
                $config = config('services.twilio');

                $twilio = new Client(
                    $config['account_sid'],
                    $config['auth_token'],
                );

                return new TwilioClient(
                    twilio: $twilio,
                    twilioMessagingServiceSid: $config['messaging_service_sid']
                );
            });


        Notification::resolved(static function (ChannelManager $service) {
            $service->extend('twilio', static fn($app) => $app->make(TwilioChannel::class));
        });
    }
}
