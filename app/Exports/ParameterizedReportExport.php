<?php

namespace App\Exports;

use App\Models\Victim\Instance;
use \Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;


class ParameterizedReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStyles
{
    protected $instances;

    public function __construct(Collection $instances)
    {
        $this->instances = $instances;
    }


    /**
     * @return Collection
     */
    public function collection()
    {
        $collection = $this->instances->map(function (Instance $instance, $index) {
            $healthZone = $instance->healthCenter->healthZone;

            return [
                __("No.") => $index + 1,
                __("Province") => $healthZone->province,
                __("Zone de santé") => $healthZone->name,
                __("Centre de santé") => $instance->healthCenter->name,
                __("Code Survivant(e)") => $instance->survivor->code,
                __("Date ouverture dossier") => $instance->created_at->isoFormat('D MMMM YYYY'),
                __("Type Final") => $instance->type->getLabel(),
                __("Statut") => $instance->status->getLabel(),
                __("Prises en charges") => $instance->treatmentsLabels() ?? '-',
                __("Nombre de suivis") => $instance->followups()->count(),
                __("Accompagnants") => $instance->companions->map(fn($companion) => $companion->user->name)->join(', '),
                __("Rechute") => $instance->relapse ? 'Oui' : 'Non',
                __("Date rechute") => $instance->relapse?->created_at->isoFormat('D MMMM YYYY') ?? '-',
                __("Cause Rechute") => $instance->relapse->description ?? '-',
            ];
        });

        return $collection;
    }


    public function styles(Worksheet $sheet)
    {
        $style = $sheet->getStyle('A1:L1');

        $style->getFont()->setBold(true);

        $style->getFill()->applyFromArray([
            'fillType' => 'solid',
            'rotation' => 0,
            'color' => [
                'rgb' => 'D9D9D9'
            ],
        ]);
    }


    public function headings(): array
    {
        $collect = collect($this->collection()->first());

        return $collect->keys()->toArray();
    }
}
