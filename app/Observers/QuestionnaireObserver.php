<?php

namespace App\Observers;

use App\Models\Questionnaire;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;


class QuestionnaireObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Questionnaire "created" event.
     */
    public function created(Questionnaire $questionnaire): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $questionnaire->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $questionnaire->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Questionnaire "updated" event.
     */
    public function updated(Questionnaire $questionnaire): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $questionnaire->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $questionnaire->getChanges(),
                data: $questionnaire->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Questionnaire "deleted" event.
     */
    public function deleted(Questionnaire $questionnaire): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $questionnaire->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $questionnaire->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Questionnaire "restored" event.
     */
    public function restored(Questionnaire $questionnaire): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $questionnaire->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $questionnaire->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Questionnaire "force deleted" event.
     */
    public function forceDeleted(Questionnaire $questionnaire): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $questionnaire->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $questionnaire->withoutRelations()->toArray(),
            )
        );
    }
}
