<?php

namespace App\Observers;

use App\Enums\UserRole;
use App\Models\User;
use App\Models\Victim\Companion;
use App\Notifications;
use App\Models\Victim\Relapse;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;


class RelapseObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Relapse "created" event.
     */
    public function created(Relapse $relapse): void
    {
        $this->getUsers($relapse)
            ->each(function (User $user) use ($relapse) {
                $user->notify(new Notifications\RelapseCreated($relapse));
            });


        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $relapse->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $relapse->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Relapse "updated" event.
     */
    public function updated(Relapse $relapse): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $relapse->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $relapse->getChanges(),
                data: $relapse->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Relapse "deleted" event.
     */
    public function deleted(Relapse $relapse): void
    {
        $this->getUsers($relapse)
            ->each(function (User $user) use ($relapse) {
                $user->notify(new Notifications\RelapseRemoved($relapse->instance));
            });


        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $relapse->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $relapse->withoutRelations()->toArray(),
            )
        );
    }

    private function getUsers(Relapse $relapse)
    {
        $users = $relapse->instance->healthCenter->users
            ->filter(fn(User $user) => $user->role !== UserRole::COMPANION)
            ->filter(fn(User $user) => $user->id !== auth()->user()?->id);

        $companionUsers = $relapse->instance
            ->companions
            ->map(fn(Companion $companion) => $companion->user)
            ->filter(fn(User $user) => $user->id !== auth()->user()?->id);

        return $users->merge($companionUsers);
    }

    /**
     * Handle the Relapse "restored" event.
     */
    public function restored(Relapse $relapse): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $relapse->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $relapse->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Relapse "force deleted" event.
     */
    public function forceDeleted(Relapse $relapse): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $relapse->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $relapse->withoutRelations()->toArray(),
            )
        );
    }
}
