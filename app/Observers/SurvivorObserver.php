<?php

namespace App\Observers;

use App\Models\Victim\Survivor;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;


class SurvivorObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Survivor "created" event.
     */
    public function created(Survivor $survivor): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $survivor->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $survivor->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Survivor "updated" event.
     */
    public function updated(Survivor $survivor): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $survivor->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $survivor->getChanges(),
                data: $survivor->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Survivor "deleted" event.
     */
    public function deleted(Survivor $survivor): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $survivor->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $survivor->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Survivor "restored" event.
     */
    public function restored(Survivor $survivor): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $survivor->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $survivor->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Survivor "force deleted" event.
     */
    public function forceDeleted(Survivor $survivor): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $survivor->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $survivor->withoutRelations()->toArray(),
            )
        );
    }
}
