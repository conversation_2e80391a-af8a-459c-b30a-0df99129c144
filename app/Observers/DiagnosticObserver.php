<?php

namespace App\Observers;

use App\Models\Victim\Diagnostic;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;


class DiagnosticObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Diagnostic "created" event.
     */
    public function created(Diagnostic $diagnostic): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $diagnostic->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $diagnostic->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Diagnostic "updated" event.
     */
    public function updated(Diagnostic $diagnostic): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $diagnostic->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $diagnostic->getChanges(),
                data: $diagnostic->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Diagnostic "deleted" event.
     */
    public function deleted(Diagnostic $diagnostic): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $diagnostic->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $diagnostic->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Diagnostic "restored" event.
     */
    public function restored(Diagnostic $diagnostic): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $diagnostic->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $diagnostic->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Diagnostic "force deleted" event.
     */
    public function forceDeleted(Diagnostic $diagnostic): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $diagnostic->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $diagnostic->withoutRelations()->toArray(),
            )
        );
    }
}
