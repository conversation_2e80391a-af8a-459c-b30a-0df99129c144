<?php

namespace App\Observers;

use App\Models\Victim\Followup;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;


class FollowupObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Followup "created" event.
     */
    public function created(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "updated" event.
     */
    public function updated(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $Followup->getChanges(),
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "deleted" event.
     */
    public function deleted(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "restored" event.
     */
    public function restored(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "force deleted" event.
     */
    public function forceDeleted(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }
}
