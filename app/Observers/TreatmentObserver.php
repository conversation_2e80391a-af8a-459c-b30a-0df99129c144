<?php

namespace App\Observers;

use App\Models\Victim\Treatment;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class TreatmentObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Treatment "created" event.
     */
    public function created(Treatment $treatment): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $treatment->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $treatment->withoutRelations()->toArray(),
            )
        );

        // clear treatment observation dashes
        $this->clearTreatmentObservationDashes($treatment, '-----');
        $this->clearTreatmentObservationDashes($treatment, '----');
    }

    /**
     * Handle the Treatment "updated" event.
     */
    public function updated(Treatment $treatment): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $treatment->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $treatment->getChanges(),
                data: $treatment->withoutRelations()->toArray(),
            )
        );


        // Update treatment observation dashes
        $this->clearTreatmentObservationDashes($treatment, '-----');
        $this->clearTreatmentObservationDashes($treatment, '----');
    }

    /**
     * Handle the Treatment "deleted" event.
     */
    public function deleted(Treatment $treatment): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $treatment->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $treatment->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Treatment "restored" event.
     */
    public function restored(Treatment $treatment): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $treatment->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $treatment->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Treatment "force deleted" event.
     */
    public function forceDeleted(Treatment $treatment): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $treatment->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $treatment->withoutRelations()->toArray(),
            )
        );
    }

    protected function clearTreatmentObservationDashes(Treatment $treatment, string $symbol)
    {
        if (trim($treatment->observation) !== $symbol) {
            $treatment->observation = str_replace($symbol, '', $treatment->observation);
            $treatment->saveQuietly();
        }
    }
}
