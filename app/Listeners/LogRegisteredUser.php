<?php

namespace App\Listeners;

use App\Models\Activity;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogRegisteredUser
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                action: \App\Enums\ActivityActionType::REGISTER,
                model: $event->user->getTable(),
                data: [],
                attributes: []
            )
        );
    }
}
