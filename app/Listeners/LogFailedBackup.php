<?php

namespace App\Listeners;

use <PERSON><PERSON>\Backup\Events\BackupHasFailed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogFailedBackup
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BackupHasFailed $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                action: \App\Enums\ActivityActionType::BACKUP_FAILED,
                model: "backup",
                data: [
                    "backupName" => $event->backupDestination->backupName(),
                    "diskName" => $event->backupDestination->diskName(),
                    "error" => $event->exception->getMessage()
                ],
                attributes: []
            )
        );
    }
}
