<?php

namespace App\Listeners;

use <PERSON><PERSON>\Backup\Events\BackupWasSuccessful;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogSuccessfulBackup
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BackupWasSuccessful $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                action: \App\Enums\ActivityActionType::BACKUP_SUCCESSFUL,
                model: "backup",
                data: [
                    "backupName" => $event->backupDestination->backupName(),
                    "diskName" => $event->backupDestination->diskName(),
                ],
                attributes: []
            )
        );
    }
}
