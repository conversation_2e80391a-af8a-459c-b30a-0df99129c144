<?php

namespace App\Listeners;

use App\Models\Activity;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogSuccessfulLogin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                action: \App\Enums\ActivityActionType::LOGIN,
                model: $event->user->getTable(),
                data: [
                    'type' => 'WEB',
                    'at' => now()->toString()
                ],
                attributes: []
            )
        );
    }
}
