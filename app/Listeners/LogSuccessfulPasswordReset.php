<?php

namespace App\Listeners;

use App\Models\Activity;
use App\Events\PasswordReset;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogSuccessfulPasswordReset
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PasswordReset $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                model: $event->user->getTable(),
                action: \App\Enums\ActivityActionType::PASSWORD_RESET,
                data: [],
                attributes: []
            )
        );
    }
}
