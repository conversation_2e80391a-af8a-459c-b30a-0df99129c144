<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;


enum InstanceStatus: string implements HasColor, HasIcon, HasLabel
{
    case OPEN = 'OPEN';

    case CLOSED = 'CLOSED';


    public function getLabel(): string
    {
        return match ($this) {
            self::OPEN => __('Ouvert'),
            self::CLOSED => __('Fermé'),
        };
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::OPEN => 'heroicon-o-folder-open',
            self::CLOSED => 'heroicon-o-lock-closed'
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::OPEN => 'success',
            self::CLOSED => 'warning'
        };
    }
}

