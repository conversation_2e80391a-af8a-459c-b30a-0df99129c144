<?php


namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum UserResponsibility: string implements <PERSON><PERSON>abe<PERSON>
{
    case ADMINISTRATOR = "ADMINISTRATOR";

    case OPERATOR = "OPERATOR";

    case COMPANION = "COMPANION";


    public static function transAPS()
    {
        return [
            self::OPERATOR->value => self::OPERATOR->getLabel(),
            self::ADMINISTRATOR->value => self::ADMINISTRATOR->getLabel()
        ];
    }


    public static function transCompanion()
    {
        return [
            self::COMPANION->value => self::COMPANION->getLabel(),
        ];
    }


    public static function trans()
    {
        return [
            self::OPERATOR->value => self::OPERATOR->getLabel(),
            self::ADMINISTRATOR->value => self::ADMINISTRATOR->getLabel(),
            self::COMPANION->value => self::COMPANION->getLabel(),
        ];
    }

    /**
     * @return string|null
     */
    public function getLabel(): string|null
    {
        return match ($this) {
            self::OPERATOR => __("Operateur"),
            self::ADMINISTRATOR => __("Administateur"),
            self::COMPANION => __("Accompagnant"),
        };
    }
}
