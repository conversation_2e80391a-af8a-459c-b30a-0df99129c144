<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum QuestionnaireResponseType: string implements HasLabel
{
    case TEXT = "TEXT";

    case CHOICES = "CHOICES";

    case BOOLEAN = "BOOLEAN";

    case NUMBER = "NUMBER";

    case DATE = "DATE";

    case ATTACHMENT = "ATTACHMENT";

    /**
     * @return string|null
     */
    public function getLabel(): string|null
    {
        return match ($this) {
            self::TEXT => __("Texte"),
            self::CHOICES => __("Choix Multiple"),
            self::BOOLEAN => __("Oui_Non"),
            self::NUMBER => __("Nombre"),
            self::DATE => __("Date"),
            self::ATTACHMENT => __("Pièce jointe"),
        };
    }
}

