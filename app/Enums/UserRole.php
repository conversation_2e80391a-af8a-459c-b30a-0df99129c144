<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum UserRole: string implements HasLabel
{
    // Application Root
    case ROOT = "ROOT";

    // Clinic APS
    case APS = "APS";

    // Click Companion
    case COMPANION = "COMPANION";

    public function getLabel(): string|null
    {
        return match ($this) {
            self::ROOT => __("Système Admin"),
            self::APS => __("APS"),
            self::COMPANION => __("Accompagnant"),
        };
    }


    public static function options(): array
    {
        return [
            self::ROOT->value => self::ROOT->getLabel(),
            self::APS->value => self::APS->getLabel(),
            self::COMPANION->value => self::COMPANION->getLabel(),
        ];
    }
}