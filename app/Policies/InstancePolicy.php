<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Victim\Instance;
use Illuminate\Auth\Access\Response;

class InstancePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->isRoot() || $user->isAPS();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Instance $instance): bool
    {
        return $user->isRoot() || $user->isAPS();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isRoot() || $user->isAPS();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Instance $instance): bool
    {
        return $user->isRoot() || $user->isAPS();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Instance $instance): bool
    {
        return $user->isRoot() || $user->isAPS();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Instance $instance): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Instance $instance): bool
    {
        return $user->isRoot();
    }
}
