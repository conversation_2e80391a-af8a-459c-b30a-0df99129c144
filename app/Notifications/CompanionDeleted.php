<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Victim\Survivor;
use App\Models\Health\HealthCenter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Filament\Notifications\Notification as FilamentNotification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class CompanionDeleted extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Survivor $survivor,
        public readonly HealthCenter $healthCenter,
    ) {
        $this->afterCommit();
    }


    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $survivorCode = $this->survivor->code;
        $healthCenterName = $this->healthCenter->name;

        return FilamentNotification::make()
            ->info()
            ->title(__("Dessaisissement d'Accompagnant"))
            ->body(__("{$notifiable->name}, vous avez été dessaisi en tant qu'Accompagnant du cas $survivorCode au $healthCenterName."))
            ->getDatabaseMessage();
    }

    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm($notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return (
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                )
            )
        );
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
