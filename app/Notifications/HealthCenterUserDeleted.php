<?php

namespace App\Notifications;

use App\Models\Health\HealthCenter;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Filament\Notifications\Notification as FilamentNotification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class HealthCenterUserDeleted extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly HealthCenter $healthCenter
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $healthCenterName = $this->healthCenter->name;

        return FilamentNotification::make()
            ->info()
            ->title(__("Retrait de centre de santé"))
            ->body(__("{$notifiable->name}, vous avez été retiré du centre de santé $healthCenterName."))
            ->getDatabaseMessage();
    }


    public function toFcm($notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return (
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                )
            )
        );
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
