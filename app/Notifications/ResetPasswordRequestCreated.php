<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use App\NotificationChannels\Twilio\TwilioMessage;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Filament\Notifications\Notification as FilamentNotification;

class ResetPasswordRequestCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public string $token,
        public string $password,
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['twilio'];
    }

    /**
     * Get the twilio representation of the notification.
     */
    public function toTwilio($notifiable)
    {
        $message = (object) $this->toDatabase($notifiable);

        return (new TwilioMessage($message->body))->toASCII();
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        return FilamentNotification::make()
            ->info()
            ->title(__('Réinitialisation du mot de passe'))
            ->body(__("Code de réinitialisation : {$this->token}.\nNouveau Mot de passe : {$this->password}.\nPrudence, ce code expirera dans les 30 min."))
            ->getDatabaseMessage();
    }
}
