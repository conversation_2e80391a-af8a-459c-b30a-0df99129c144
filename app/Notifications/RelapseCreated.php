<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Victim\Relapse;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Filament\Notifications\Notification as FilamentNotification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class RelapseCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Relapse $relapse,
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', FcmChannel::class];
    }


    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $instance = $this->relapse->instance;
        $survivor = $instance->survivor->code;
        $healCenter = $instance->healthCenter->name;

        return FilamentNotification::make()
            ->warning()
            ->title(__("Cas de rechute"))
            ->body(__("{$notifiable->name}, rechute détectée pour le cas $survivor au $healCenter."))
            ->getDatabaseMessage();
    }


    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm($notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return (
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                )
            )
        );
    }


    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
