<?php

namespace App\Notifications\Backup;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Spatie\Backup\Notifications\Notifications\BackupHasFailedNotification as BaseNotification;
use NotificationChannels\Telegram\TelegramMessage;

class BackupHasFailedNotification extends BaseNotification
{
    use Queueable;


    public function toTelegram($notifiable)
    {
        $subject = trans('backup::notifications.backup_failed_subject', ['application_name' => $this->applicationName()]);

        $message = TelegramMessage::create()
            ->to($notifiable->routeNotificationForTelegram())
            ->content("🔴\n$subject\n")
            ->line(trans('backup::notifications.backup_failed_body', ['application_name' => $this->applicationName()]))
            ->line(trans('backup::notifications.exception_message', ['message' => $this->event->exception->getMessage()]))
            ->line(trans('backup::notifications.exception_trace', ['trace' => $this->event->exception->getTraceAsString()]));

        $this->backupDestinationProperties()->each(fn($value, $name) => $message->line("{$name}: $value"));

        return $message;
    }
}
