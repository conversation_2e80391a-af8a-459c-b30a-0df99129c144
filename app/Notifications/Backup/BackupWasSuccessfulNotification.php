<?php

namespace App\Notifications\Backup;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Spatie\Backup\Notifications\Notifications\BackupWasSuccessfulNotification as BaseNotification;
use NotificationChannels\Telegram\TelegramMessage;


class BackupWasSuccessfulNotification extends BaseNotification
{
    use Queueable;


    public function toTelegram($notifiable)
    {
        $subject = trans('backup::notifications.backup_successful_subject', ['application_name' => $this->applicationName()]);

        $message = TelegramMessage::create()
            ->to($notifiable->routeNotificationForTelegram())
            ->content("✅\n$subject\n")
            ->line(trans('backup::notifications.backup_successful_body', ['application_name' => $this->applicationName(), 'disk_name' => $this->diskName()]));

        $this->backupDestinationProperties()->each(function ($value, $name) use ($message) {
            $message->line("{$name}: $value");
        });

        return $message;
    }
}
