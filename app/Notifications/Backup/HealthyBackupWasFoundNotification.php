<?php

namespace App\Notifications\Backup;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use NotificationChannels\Telegram\TelegramMessage;
use Spatie\Backup\Notifications\Notifications\HealthyBackupWasFoundNotification as BaseNotification;

class HealthyBackupWasFoundNotification extends BaseNotification
{
    use Queueable;

    public function toTelegram($notifiable)
    {
        $subject = trans('backup::notifications.healthy_backup_found_subject', ['application_name' => $this->applicationName(), 'disk_name' => $this->diskName()]);

        $message = TelegramMessage::create()
            ->to($notifiable->routeNotificationForTelegram())
            ->content("🟢\n$subject\n")
            ->line(trans('backup::notifications.healthy_backup_found_body', ['application_name' => $this->applicationName()]));

        $this->backupDestinationProperties()->each(function ($value, $name) use ($message) {
            $message->line("{$name}: $value");
        });

        return $message;
    }
}
