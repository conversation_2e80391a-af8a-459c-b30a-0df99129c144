<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Victim\Companion;
use App\NotificationChannels\Twilio\TwilioMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Filament\Notifications\Notification as FilamentNotification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;


class CompanionCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Companion $companion
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'twilio', FcmChannel::class];
    }

    /**
     * Get the twilio representation of the notification.
     */
    public function toTwilio($notifiable)
    {
        $message = (object) $this->toDatabase($notifiable);

        return (new TwilioMessage($message->body))->toASCII();
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $instance = $this->companion->instance;
        $companionType = $this->companion->type->getLabel();

        $survivorCode = $instance->survivor->code;
        $healthCenterName = $instance->healthCenter->name;

        return FilamentNotification::make()
            ->success()
            ->title(__("Nouveau cas assigné"))
            ->body(__("{$notifiable->name}, vous avez été assigné en tant qu'Accompagnant $companionType au cas $survivorCode (Code Survivant).\nCentre de santé: $healthCenterName"))
            ->getDatabaseMessage();
    }


    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm($notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return (
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                )
            )
        );
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
